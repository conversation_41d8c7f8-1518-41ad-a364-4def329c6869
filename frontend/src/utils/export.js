import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'
import { formatDate } from './format'

/**
 * 等待所有图片和图表加载完成
 * @param {HTMLElement} element - 要检查的HTML元素
 * @returns {Promise<void>}
 */
async function waitForContent(element) {
  // 等待图片加载
  const images = element.querySelectorAll('img')
  const imagePromises = Array.from(images).map(img => {
    if (img.complete) return Promise.resolve()
    return new Promise((resolve, reject) => {
      img.onload = resolve
      img.onerror = resolve // 即使图片加载失败也继续
      setTimeout(resolve, 3000) // 3秒超时
    })
  })

  // 等待Canvas元素（图表）渲染完成
  const canvases = element.querySelectorAll('canvas')
  if (canvases.length > 0) {
    // 给图表一些时间完成渲染
    await new Promise(resolve => setTimeout(resolve, 1000))
  }

  await Promise.all(imagePromises)
}

/**
 * 将HTML元素导出为PDF（支持多页）
 * @param {HTMLElement} element - 要导出的HTML元素
 * @param {string} filename - PDF文件名（不包含扩展名）
 * @param {Object} options - 导出选项
 * @returns {Promise<void>}
 */
export async function exportToPDF(element, filename, options = {}) {
  try {
    // 默认选项
    const defaultOptions = {
      scale: 2,
      useCORS: true,
      logging: false,
      backgroundColor: '#ffffff',
      allowTaint: true,
      foreignObjectRendering: true,
      imageTimeout: 5000,
      removeContainer: true
    }

    const finalOptions = { ...defaultOptions, ...options }

    // 等待内容加载完成
    await waitForContent(element)

    // 临时修改样式以优化导出效果
    const originalStyles = prepareElementForExport(element)

    // 创建canvas
    const canvas = await html2canvas(element, finalOptions)

    // 恢复原始样式
    restoreElementStyles(element, originalStyles)

    // PDF页面设置
    const pageWidth = 595.28 // A4纸的宽度（单位：pt）
    const pageHeight = 841.89 // A4纸的高度（单位：pt）
    const margin = 40 // 页边距
    const contentWidth = pageWidth - 2 * margin
    const contentHeight = pageHeight - 2 * margin

    // 计算缩放比例和居中位置
    const canvasWidth = canvas.width
    const canvasHeight = canvas.height
    const ratio = Math.min(contentWidth / canvasWidth, contentHeight / canvasHeight)
    const scaledWidth = canvasWidth * ratio
    const scaledHeight = canvasHeight * ratio

    // 计算水平居中位置
    const xOffset = margin + (contentWidth - scaledWidth) / 2

    // 创建PDF实例
    const pdf = new jsPDF('p', 'pt', 'a4')

    // 如果内容高度小于等于一页，直接添加
    if (scaledHeight <= contentHeight) {
      const imgData = canvas.toDataURL('image/jpeg', 0.9)
      // 计算垂直居中位置
      const yPosition = margin + (contentHeight - scaledHeight) / 2
      pdf.addImage(imgData, 'JPEG', xOffset, Math.max(margin, yPosition), scaledWidth, scaledHeight)
    } else {
      // 多页处理
      const totalPages = Math.ceil(scaledHeight / contentHeight)

      for (let i = 0; i < totalPages; i++) {
        if (i > 0) {
          pdf.addPage()
        }

        // 计算当前页的源区域
        const sourceY = (i * contentHeight) / ratio
        const sourceHeight = Math.min(contentHeight / ratio, canvasHeight - sourceY)
        const targetHeight = sourceHeight * ratio
        const targetWidth = canvasWidth * ratio

        // 创建临时canvas来裁剪当前页内容
        const tempCanvas = document.createElement('canvas')
        const tempCtx = tempCanvas.getContext('2d')
        tempCanvas.width = canvasWidth
        tempCanvas.height = sourceHeight

        // 绘制当前页内容
        tempCtx.drawImage(
          canvas,
          0, sourceY, canvasWidth, sourceHeight,
          0, 0, canvasWidth, sourceHeight
        )

        // 添加到PDF，内容水平居中
        const pageImgData = tempCanvas.toDataURL('image/jpeg', 0.9)
        const pageXOffset = margin + (contentWidth - targetWidth) / 2
        pdf.addImage(pageImgData, 'JPEG', pageXOffset, margin, targetWidth, targetHeight)
      }
    }

    // 添加页脚信息
    addFooterToPDF(pdf, filename)

    // 生成PDF文件名
    const timestamp = formatDate(new Date(), 'YYYYMMDDHHmm')
    const fullFilename = `${filename}_${timestamp}.pdf`

    // 保存PDF
    pdf.save(fullFilename)
  } catch (error) {
    console.error('导出PDF失败:', error)
    throw error
  }
}

/**
 * 准备元素以优化导出效果
 * @param {HTMLElement} element - 要准备的元素
 * @returns {Map} 原始样式映射
 */
function prepareElementForExport(element) {
  const originalStyles = new Map()

  // 确保所有内容可见
  const hiddenElements = element.querySelectorAll('[style*="display: none"], .hidden')
  hiddenElements.forEach(el => {
    originalStyles.set(el, el.style.display)
    el.style.display = 'block'
  })

  // 优化图表容器
  const chartContainers = element.querySelectorAll('.chart-container, [class*="chart"]')
  chartContainers.forEach(container => {
    if (!originalStyles.has(container)) {
      originalStyles.set(container, {
        minHeight: container.style.minHeight,
        height: container.style.height
      })
    }
    container.style.minHeight = 'auto'
    if (container.style.height && container.style.height.includes('px')) {
      // 保持现有高度
    }
  })

  // 确保表格完整显示
  const tables = element.querySelectorAll('table')
  tables.forEach(table => {
    if (!originalStyles.has(table)) {
      originalStyles.set(table, {
        pageBreakInside: table.style.pageBreakInside
      })
    }
    table.style.pageBreakInside = 'avoid'
  })

  return originalStyles
}

/**
 * 恢复元素的原始样式
 * @param {HTMLElement} element - 要恢复的元素
 * @param {Map} originalStyles - 原始样式映射
 */
function restoreElementStyles(element, originalStyles) {
  originalStyles.forEach((style, el) => {
    if (typeof style === 'string') {
      el.style.display = style
    } else if (typeof style === 'object') {
      Object.keys(style).forEach(prop => {
        el.style[prop] = style[prop]
      })
    }
  })
}

/**
 * 为PDF添加页脚信息
 * @param {jsPDF} pdf - PDF实例
 * @param {string} title - 报告标题
 */
function addFooterToPDF(pdf, title) {
  const pageCount = pdf.internal.getNumberOfPages()
  const pageWidth = pdf.internal.pageSize.getWidth()
  const pageHeight = pdf.internal.pageSize.getHeight()

  for (let i = 1; i <= pageCount; i++) {
    pdf.setPage(i)

    // 设置页脚字体和颜色
    pdf.setFontSize(8)
    pdf.setTextColor(128, 128, 128)

    // 添加生成时间（使用英文避免乱码）
    const now = new Date()
    const timeString = formatDate(now, 'YYYY-MM-DD HH:mm:ss')
    pdf.text(`Generated: ${timeString}`, 40, pageHeight - 20)

    // 添加页码（使用英文避免乱码）
    pdf.text(`Page ${i} of ${pageCount}`, pageWidth - 100, pageHeight - 20)

    // 添加标题（使用英文避免乱码）
    const englishTitle = 'Forex Technical Analysis Report'
    pdf.text(englishTitle, pageWidth / 2, pageHeight - 20, { align: 'center' })
  }
}


